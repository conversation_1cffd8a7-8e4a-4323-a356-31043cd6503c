<template>
  <a-card title="生产领料">
    <!-- 表格区域 -->
    <manage-base-table
      :columns="columns"
      :model="searchForm"
      :query="queryFn"
      ref="tableRef"
    >
      <template #searchBox>
        <a-form-item label="工单编号">
          <a-input
            v-model:value="searchForm.taskNo"
            placeholder="请输入工单编号"
            allow-clear
          ></a-input>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="resetSearch">重置</a-button>
          </a-space>
        </a-form-item>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space size="small">
            <a-button
              type="primary"
              @click="handleOutbound(record)"
              :disabled="record.status == 3"
            >
              领料
            </a-button>
            <a-button type="primary" @click="handleViewOutboundRecords(record)">
              记录
            </a-button>
          </a-space>
        </template>
      </template>
    </manage-base-table>

    <!-- 领料模态框 -->
    <a-modal
      v-model:open="outboundModalOpen"
      title="生产领料"
      @ok="handleOutboundSubmit"
      :confirmLoading="outboundModalLoading"
      width="80%"
    >
      <a-descriptions
        :title="`工单编号: ${outboundForm.taskNo}`"
        :column="3"
        bordered
      >
        <a-descriptions-item label="工单编号">
          {{ outboundForm.taskNo }}
        </a-descriptions-item>
        <a-descriptions-item label="产出品名称">
          {{ outboundForm.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="计划数量">
          {{ outboundForm.planQuantity }}
        </a-descriptions-item>
      </a-descriptions>
      <a-divider>领料清单</a-divider>
      <manage-materiel-form-table
        v-model:source="bomItems"
        :is-view="false"
        :has-add="false"
        :has-note="true"
        :has-price="false"
        :has-batch-no="true"
        :has-selected="false"
        :has-delete="false"
      />
    </a-modal>

    <!-- 领料记录详情模态框 -->
    <a-modal
      v-model:open="outboundRecordsModalOpen"
      title="领料记录详情"
      :footer="null"
      width="80%"
    >
      <a-descriptions
        :title="`工单编号: ${currentTask.code}`"
        :column="3"
        bordered
      >
        <a-descriptions-item label="工单编号">
          {{ currentTask.code }}
        </a-descriptions-item>
        <a-descriptions-item label="产出品名称">
          {{ currentTask.productName }}
        </a-descriptions-item>
        <a-descriptions-item label="计划数量">
          {{ currentTask.quantity }}
        </a-descriptions-item>
      </a-descriptions>
      <a-divider>领料记录列表</a-divider>

      <!-- 领料记录表格 -->
      <a-table
        :columns="outboundRecordsColumns"
        :dataSource="outboundRecords"
        :loading="outboundRecordsLoading"
        rowKey="id"
        :pagination="false"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'quantity'">
            {{ Number(record.quantity) }}
          </template>
          <template v-if="column.key === 'createdAt'">
            {{ dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </template>
      </a-table>

      <div style="margin-top: 16px; text-align: right">
        <a-button @click="outboundRecordsModalOpen = false">关闭</a-button>
      </div>
    </a-modal>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 表格列定义
  const columns = [
    { title: "工单编号", dataIndex: "code", key: "code", width: 110 },
    {
      title: "产品名称",
      key: "productName",
      customRender: ({ record }: { record: any }) => {
        return record.Materiel?.name || "-";
      },
      minWidth: 110,
      resizable: true,
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
      key: "quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
      width: 110,
    },
    {
      title: "计划开始时间",
      dataIndex: "startAt",
      key: "startAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
      width: 110,
    },
    {
      title: "计划结束时间",
      dataIndex: "endAt",
      key: "endAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
      width: 110,
    },
    { title: "状态", dataIndex: "status", key: "status", width: 100 },
    { title: "操作", key: "action", width: 180, align: "center" },
  ];

  // 搜索表单
  const searchForm = reactive({
    taskNo: "",
  });

  // 领料表单
  const outboundForm = reactive({
    id: null as number | null,
    taskNo: "",
    productName: "",
    planQuantity: 0,
    materiel_id: null as number | null,
    warehouse_id: null as number | null,
    batch_no: "",
    quantity: 1,
    note: "",
  });

  // 模态框状态
  const outboundModalOpen = ref(false);
  const outboundModalLoading = ref(false);
  const tableRef = useTemplateRef("tableRef");

  // BOM清单数据
  const bomItems = ref<MaterielListItem[]>([]);
  const selectedMaterial = ref<any>(null);

  // 领料记录相关状态
  const outboundRecordsModalOpen = ref(false);
  const outboundRecordsLoading = ref(false);
  const outboundRecords = ref<any[]>([]);
  const currentTask = ref<any>({
    id: null,
    code: "",
    productName: "",
    quantity: 0,
  });

  // 领料记录表格列定义
  const outboundRecordsColumns = [
    { title: "物料编码", dataIndex: ["materiel", "code"], key: "materielCode" },
    { title: "物料名称", dataIndex: ["materiel", "name"], key: "materielName" },
    { title: "批号", dataIndex: "batch_no", key: "batchNo" },
    { title: "领料数量", dataIndex: "quantity", key: "quantity" },
    { title: "备注", dataIndex: "note", key: "note" },
    { title: "领料时间", dataIndex: "createdAt", key: "createdAt" },
  ];

  // 查询函数
  const queryFn = async (params: any) => {
    const { $client } = useNuxtApp();
    const result = await $client.admin.production.queryProductionTask.query({
      code: searchForm.taskNo,
      // status: "pending",
      ...params,
    });
    return result;
  };

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "default";
      case 1:
        return "processing";
      case 2:
        return "processing";
      case 3:
        return "success";
      case 4:
        return "error";
      default:
        return "default";
    }
  };

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "草稿";
      case 1:
        return "待领料";
      case 2:
        return "生产中";
      case 3:
        return "已完成";
      case 4:
        return "已终止";
      default:
        return "未知状态";
    }
  };

  // 处理搜索
  const handleSearch = () => {
    tableRef.value?.query();
  };

  // 重置搜索
  const resetSearch = () => {
    searchForm.taskNo = "";
    handleSearch();
  };

  // 处理领料
  const handleOutbound = async (record: any) => {
    outboundForm.id = record.id;
    outboundForm.taskNo = record.code;
    outboundForm.productName = record.Materiel?.name || "";
    outboundForm.planQuantity = Number(record.quantity) || 0;
    outboundForm.materiel_id = record.materiel_id;
    outboundForm.warehouse_id = null;
    outboundForm.batch_no = "";
    outboundForm.quantity = 1;
    outboundForm.note = "";
    selectedMaterial.value = null;

    try {
      // 获取BOM清单
      const response = await useApiTrpc().admin.bom.queryBom.query({
        materielId: record.materiel_id,
      });
      bomItems.value =
        response.data.result.map((item: any) => ({
          uid: item.child.id,
          id: item.child.id,
          code: item.child.code,
          name: item.child.name,
          model: item.child.model,
          unit: item.child.unit,
          specification: item.child.specification,
          quantity: item.quantity * outboundForm.planQuantity,
        })) || [];
    } catch (error: any) {
      message.error(error.message || "获取BOM清单失败");
    }

    outboundModalOpen.value = true;
  };

  // 提交领料
  const handleOutboundSubmit = async () => {
    Modal.confirm({
      title: "确认领料",
      content:
        "确认领料后，将按照领料清单中的数量进行出库,请仔细核对数量是否相符!",
      onOk: async (close) => {
        if (!outboundForm.id) {
          message.error("请选择工单");
          return;
        }
        const result =
          await useApiTrpc().admin.production.productionOutbound.mutate({
            task_id: outboundForm.id,
            bom_items: bomItems.value.map((item) => ({
              id: item.id,
              batch_no: item.batch_no || "",
              quantity: Number(item.quantity),
            })),
          });
        // console.log("领料", outboundForm, bomItems.value);
        if (result.code === 1) {
          message.success("领料成功");
          outboundModalOpen.value = false;
          tableRef.value?.query();
          //关闭领料记录模态框
          close();
        } else {
          message.error(result.message || "领料失败");
        }
      },
      okText: "我已确认",
    });
  };

  // 查看领料记录
  const handleViewOutboundRecords = async (record: any) => {
    try {
      // 设置当前工单信息
      currentTask.value = {
        id: record.id,
        code: record.code,
        productName: record.Materiel?.name || "",
        quantity: Number(record.quantity) || 0,
      };

      // 显示加载状态
      outboundRecordsLoading.value = true;
      outboundRecordsModalOpen.value = true;

      // 查询领料记录
      const result =
        await useApiTrpc().admin.production.queryProductionOutRecords.query({
          taskId: record.id,
          take: 100, // 获取较多记录
          skip: 0,
        });

      if (result.code === 1) {
        outboundRecords.value = result.data.result || [];
      } else {
        message.error(result.message || "获取领料记录失败");
        outboundRecords.value = [];
      }
    } catch (error: any) {
      message.error(error.message || "获取领料记录失败");
      outboundRecords.value = [];
    } finally {
      outboundRecordsLoading.value = false;
    }
  };
</script>
