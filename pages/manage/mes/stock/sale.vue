<template>
  <a-card title="销售出库">
    <template #extra>
      <a-button type="primary" @click="handleAdd">
        <template #icon><plus-outlined /></template>
        新增销售出库
      </a-button>
    </template>
    <!-- 数据表格 -->
    <manage-base-table
      ref="tableRef"
      :columns="columns"
      :query="querySalesOutRecords"
      :model="searchForm"
      rowKey="id"
    >
      <template #searchBox>
        <a-form-item name="order_no" label="订单编号">
          <a-input
            v-model:value="searchForm.order_no"
            placeholder="请输入订单编号"
            allowClear
          />
        </a-form-item>
        <a-form-item name="customer_id" label="客户">
          <a-select
            v-model:value="searchForm.customer_id"
            placeholder="请选择客户"
            allowClear
            :options="customerOptions"
            :fieldNames="{ label: 'comname', value: 'id' }"
          />
        </a-form-item>
        <a-form-item name="warehouse_id" label="仓库">
          <manage-warehouse-selector
            v-model:value="searchForm.warehouse_id"
            placeholder="请选择仓库"
          />
        </a-form-item>
        <a-form-item name="dateRange" label="日期范围">
          <a-range-picker
            v-model:value="dateRange"
            :format="dateFormat"
            @change="handleDateChange"
          />
        </a-form-item>
      </template>
      <template #headerButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><plus-outlined /></template>
          新增销售出库
        </a-button>
      </template>
    </manage-base-table>

    <!-- 销售出库表单弹窗 -->
    <a-modal
      v-model:open="modalOpen"
      title="销售出库"
      :width="700"
      :footer="null"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item name="customer_id" label="客户">
          <a-select
            v-model:value="formState.customer_id"
            placeholder="请选择客户"
            :options="customerOptions"
            :fieldNames="{ label: 'comname', value: 'id' }"
          />
        </a-form-item>
        <a-form-item name="warehouse_id" label="仓库">
          <manage-warehouse-selector
            v-model:value="formState.warehouse_id"
            placeholder="请选择仓库"
            @change="handleWarehouseChange"
            :type="['production', 'finished']"
          />
        </a-form-item>
        <a-form-item name="materiel_id" label="物料">
          <manage-materiel-form-input v-model:value="formState.materiel_id" />
        </a-form-item>
        <a-form-item name="batch_no" label="批号">
          <a-select
            v-model:value="formState.batch_no"
            placeholder="请选择批号"
            :options="batchOptions"
            @change="handleBatchChange"
          >
            <template #option="{ value, label }">
              <div>
                {{ label }}
                <div style="color: #999; font-size: 12px">
                  可用库存: {{ getBatchStock(value as string) }}
                </div>
              </div>
            </template>
          </a-select>
        </a-form-item>
        <a-form-item name="quantity" label="出库数量">
          <a-input-number
            v-model:value="formState.quantity"
            placeholder="请输入出库数量"
            :min="0.0001"
            :max="availableStock"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="order_no" label="订单编号">
          <a-input
            v-model:value="formState.order_no"
            placeholder="订单编号将自动生成"
            disabled
          />
        </a-form-item>
        <a-form-item name="note" label="备注">
          <a-textarea
            v-model:value="formState.note"
            placeholder="请输入备注"
            :rows="3"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 18, offset: 6 }">
          <a-space>
            <a-button
              type="primary"
              @click="handleSubmit"
              :loading="submitting"
            >
              提交
            </a-button>
            <a-button @click="handleCancel">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { PlusOutlined } from "@ant-design/icons-vue";
  import { reactive, ref, onMounted, watch } from "vue";
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";
  import type { Rule } from "ant-design-vue/es/form";
  import type { FormInstance } from "ant-design-vue";
  import type { SelectValue } from "ant-design-vue/es/select";
  import type { DefaultOptionType } from "ant-design-vue/es/select";

  // 表格列定义
  const columns = [
    { title: "订单编号", dataIndex: "order_no", key: "order_no" },
    { title: "客户", dataIndex: ["customer", "comname"], key: "customer" },
    { title: "物料编码", dataIndex: ["materiel", "code"], key: "materielCode" },
    { title: "物料名称", dataIndex: ["materiel", "name"], key: "materielName" },
    { title: "批号", dataIndex: "batch_no", key: "batchNo" },
    { title: "出库数量", dataIndex: "quantity", key: "quantity" },
    { title: "仓库", dataIndex: ["warehouse", "name"], key: "warehouse" },
    { title: "操作人", dataIndex: ["user", "name"], key: "user" },
    { title: "出库时间", dataIndex: "createdAt", key: "createdAt" },
    { title: "备注", dataIndex: "note", key: "note" },
  ];

  // 表格实例
  const tableRef = ref();

  // 表单实例
  const formRef = ref<FormInstance>();

  // 日期格式
  const dateFormat = "YYYY-MM-DD";

  // 日期范围
  const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 搜索表单
  const searchForm = reactive({
    order_no: "",
    customer_id: undefined,
    warehouse_id: undefined,
    startDate: "",
    endDate: "",
  });

  // 表单状态
  const formState = reactive({
    customer_id: undefined as number | undefined,
    materiel_id: undefined as number | undefined,
    warehouse_id: undefined as number | undefined,
    batch_no: "",
    quantity: 1,
    order_no: "",
    note: "",
  });

  // 表单验证规则
  const rules: Record<string, Rule[]> = {
    customer_id: [{ required: true, message: "请选择客户", trigger: "change" }],
    materiel_id: [{ required: true, message: "请选择物料", trigger: "change" }],
    warehouse_id: [
      { required: true, message: "请选择仓库", trigger: "change" },
    ],
    batch_no: [{ required: true, message: "请选择批号", trigger: "change" }],
    quantity: [{ required: true, message: "请输入出库数量", trigger: "blur" }],
    order_no: [{ required: true, message: "请输入订单编号", trigger: "blur" }],
  };

  // 模态框状态
  const modalOpen = ref(false);
  const submitting = ref(false);

  // 客户选项
  const customerOptions = ref<any[]>([]);
  // 物料选项
  const materielOptions = ref<any[]>([]);
  // 批号选项
  const batchOptions = ref<any[]>([]);
  // 库存数据
  const stockData = ref<any[]>([]);
  // 可用库存
  const availableStock = ref(0);

  // API接口
  const querySalesOutRecords =
    useApiTrpc().admin.stock.querySalesOutRecords.query;
  const salesOut = useApiTrpc().admin.stock.salesOut.mutate;
  const queryCustomers = useApiTrpc().admin.customer.queryCustomer.query;
  const queryMateriel = useApiTrpc().admin.materiel.queryMateriel.query;
  const queryStock = useApiTrpc().admin.stock.queryStock.query;

  // 日期变更处理
  const handleDateChange = (dates: any) => {
    if (dates && dates.length === 2) {
      searchForm.startDate = dates[0].format("YYYY-MM-DD");
      searchForm.endDate = dates[1].format("YYYY-MM-DD");
    } else {
      searchForm.startDate = "";
      searchForm.endDate = "";
    }
  };

  // 获取客户列表
  const fetchCustomers = async () => {
    try {
      const result = await queryCustomers({
        take: 100,
        skip: 0,
      });
      if (result.code === 1) {
        customerOptions.value = result.data.result || [];
      }
    } catch (error) {
      console.error("获取客户列表失败", error);
    }
  };

  // 获取物料列表
  const fetchMateriel = async () => {
    try {
      const result = await queryMateriel({
        take: 100,
        skip: 0,
      });
      if (result.code === 1) {
        materielOptions.value = result.data.result || [];
      }
    } catch (error) {
      console.error("获取物料列表失败", error);
    }
  };

  // 获取批号列表
  const fetchBatchOptions = async () => {
    console.log(
      "获取批号列表 - 物料ID:",
      formState.materiel_id,
      "仓库ID:",
      formState.warehouse_id
    );

    if (!formState.materiel_id || !formState.warehouse_id) {
      console.log("物料ID或仓库ID为空，无法获取批号列表");
      batchOptions.value = [];
      return;
    }

    try {
      const result = await queryStock({
        materialCode: undefined,
        materialName: undefined,
        warehouseId: formState.warehouse_id,
        take: 100,
        skip: 0,
      });

      console.log("库存查询结果:", result);

      if (result.code === 1) {
        // 过滤出当前选中物料的库存记录
        const filteredStock = (result.data.result || []).filter(
          (item: any) => item.materiel_id === formState.materiel_id
        );

        console.log("过滤后的库存记录:", filteredStock);
        stockData.value = filteredStock;

        // 转换为批号选项
        batchOptions.value = filteredStock.map((item: any) => ({
          label: `${item.batch_no} (库存: ${item.quantity})`,
          value: item.batch_no,
        }));

        console.log("批号选项:", batchOptions.value);
      }
    } catch (error) {
      console.error("获取批号列表失败", error);
    }
  };

  // 获取批号对应的库存数量
  const getBatchStock = (batchNo: any) => {
    if (!batchNo) return 0;

    const batch = String(batchNo);
    const stock = stockData.value.find(
      (item) =>
        item.batch_no === batch &&
        item.materiel_id === formState.materiel_id &&
        item.warehouse_id === formState.warehouse_id
    );
    return stock ? stock.quantity : 0;
  };

  // 仓库变更处理
  const handleWarehouseChange = () => {
    formState.batch_no = "";
    fetchBatchOptions();
  };

  // 批号变更处理
  const handleBatchChange = (value: any) => {
    if (!value) return;

    const batchNo = String(value);
    const stock = stockData.value.find(
      (item) =>
        item.batch_no === batchNo &&
        item.materiel_id === formState.materiel_id &&
        item.warehouse_id === formState.warehouse_id
    );

    if (stock) {
      availableStock.value = Number(stock.quantity);
      // 默认设置为可用库存
      formState.quantity = Math.min(1, Number(stock.quantity));
    } else {
      availableStock.value = 0;
      formState.quantity = 0;
    }

    console.log("批号变更:", batchNo, "可用库存:", availableStock.value);
  };

  // 生成销售出库订单编号
  const generateOrderNo = () => {
    const date = dayjs().format("YYYYMMDD");
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0");
    return `SO${date}${random}`;
  };

  // 新增销售出库
  const handleAdd = () => {
    // 重置表单
    formState.customer_id = undefined;
    formState.materiel_id = undefined;
    formState.warehouse_id = undefined;
    formState.batch_no = "";
    formState.quantity = 1;
    formState.order_no = generateOrderNo();
    formState.note = "";

    // 打开模态框
    modalOpen.value = true;
  };

  // 取消处理
  const handleCancel = () => {
    modalOpen.value = false;
  };

  // 提交处理
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      submitting.value = true;

      const result = await salesOut({
        customer_id: formState.customer_id!,
        materiel_id: formState.materiel_id!,
        warehouse_id: formState.warehouse_id!,
        batch_no: formState.batch_no,
        quantity: formState.quantity,
        order_no: formState.order_no,
        note: formState.note,
      });

      if (result.code === 1) {
        message.success("销售出库成功");
        modalOpen.value = false;
        tableRef.value?.query();
      } else {
        message.error(result.message || "销售出库失败");
      }
    } catch (error: any) {
      console.error("销售出库失败", error);
      message.error(error.message || "销售出库失败");
    } finally {
      submitting.value = false;
    }
  };

  // 监听物料ID和仓库ID变化
  watch(
    [() => formState.materiel_id, () => formState.warehouse_id],
    ([newMaterielId, newWarehouseId]) => {
      console.log(
        "监听到变化 - 物料ID:",
        newMaterielId,
        "仓库ID:",
        newWarehouseId
      );
      if (newMaterielId && newWarehouseId) {
        formState.batch_no = "";
        fetchBatchOptions();
      } else {
        batchOptions.value = [];
      }
    }
  );

  // 页面加载时获取数据
  onMounted(() => {
    fetchCustomers();
    fetchMateriel();
  });
</script>
